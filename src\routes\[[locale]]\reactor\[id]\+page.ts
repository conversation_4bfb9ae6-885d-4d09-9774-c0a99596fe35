import type { PageLoad } from './$types';
import type { PostEntity, CommentEntity } from './types';
import { redirect } from '@sveltejs/kit';

export const load: PageLoad = async ({ fetch, params, url }) => {
  console.dir(params, { depth: null });
  console.log(url);

  const [
    postResponse,
    commentsResponse,
  ] = await Promise.all([
    fetch(`/api/reactor/post/${params.id}`),
    fetch(`/api/reactor/comment?entityType=post&entityId=${params.id}`),
  ]);

  if (postResponse.status === 401 || commentsResponse.status === 401) {
    // redirect to /auth?redirectFrom=${url.pathname}?search
    throw redirect(302, `/auth?redirectFrom=${encodeURIComponent(url.pathname + url.search)}`);
  }

  if (!postResponse.ok) {
    throw new Error(`Post not found: ${postResponse.statusText}`);
  }

  const post: PostEntity = await postResponse.json();

  // Convert date strings to Date objects for post
  const processedPost = {
    ...post,
    createdAt: new Date(post.createdAt),
    updatedAt: new Date(post.updatedAt),
  };

  let comments: CommentEntity[] = [];
  if (commentsResponse.ok) {
    const commentsData = await commentsResponse.json() as {
      items: CommentEntity[];
      total: number;
    };

    // Convert date strings to Date objects for comments
    comments = commentsData.items.map((comment) => ({
      ...comment,
      createdAt: new Date(comment.createdAt),
      updatedAt: new Date(comment.updatedAt),
      deletedAt: comment.deletedAt ? new Date(comment.deletedAt) : null,
    }));
  }

  return {
    post: processedPost,
    comments,
  };
};
