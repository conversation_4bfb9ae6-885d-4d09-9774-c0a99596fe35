import type { PageLoad } from "./$types";
import type { PostEntity } from "./[id]/types";

import { redirect } from "@sveltejs/kit";

export const load: PageLoad = async ({ fetch, url }) => {
  console.dir(url, { depth: null });

  const response = await fetch("/api/reactor/post?page=1&size=20");

  console.log(response.ok, response.status, response.statusText)

  if (response.status === 401) {
    // redirect to /auth?redirectFrom=${url.pathname}?search
    throw redirect(302, `/auth?redirectFrom=${encodeURIComponent(url.pathname + url.search)}`);
  }

  if (!response.ok) {
    return {
      posts: [],
      isHasMorePosts: false,
    };
  }

  const data = await response.json() as {
    items: PostEntity[];
    total: number;
  };

  // Convert date strings to Date objects
  const posts = data.items.map((post) => ({
    ...post,
    createdAt: new Date(post.createdAt),
    updatedAt: new Date(post.updatedAt),
  }));

  return {
    posts,
    isHasMorePosts: data.items.length === 20, // If we got a full page, there might be more
  };
};
